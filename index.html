<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern To-Do List</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="app-header">
            <h1 class="app-title">
                <span class="title-icon">✓</span>
                To-Do List
            </h1>
            <p class="app-subtitle">Stay organized and productive</p>
        </header>

        <main class="main-content">
            <!-- Task Input Section -->
            <section class="task-input-section" aria-label="Add new task">
                <form class="task-form" id="taskForm">
                    <div class="input-group">
                        <input 
                            type="text" 
                            id="taskInput" 
                            class="task-input" 
                            placeholder="What needs to be done?"
                            aria-label="Enter new task"
                            autocomplete="off"
                            required
                        >
                        <button 
                            type="submit" 
                            class="add-btn"
                            aria-label="Add task"
                        >
                            <span class="btn-icon">+</span>
                            <span class="btn-text">Add Task</span>
                        </button>
                    </div>
                </form>
            </section>

            <!-- Task Stats Section -->
            <section class="task-stats" aria-label="Task statistics">
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number" id="totalTasks">0</span>
                        <span class="stat-label">Total</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="activeTasks">0</span>
                        <span class="stat-label">Active</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="completedTasks">0</span>
                        <span class="stat-label">Completed</span>
                    </div>
                </div>
            </section>

            <!-- Task List Section -->
            <section class="task-list-section" aria-label="Task list">
                <div class="task-controls">
                    <div class="filter-buttons" role="tablist" aria-label="Filter tasks">
                        <button 
                            class="filter-btn active" 
                            data-filter="all"
                            role="tab"
                            aria-selected="true"
                            aria-controls="taskList"
                        >
                            All
                        </button>
                        <button 
                            class="filter-btn" 
                            data-filter="active"
                            role="tab"
                            aria-selected="false"
                            aria-controls="taskList"
                        >
                            Active
                        </button>
                        <button 
                            class="filter-btn" 
                            data-filter="completed"
                            role="tab"
                            aria-selected="false"
                            aria-controls="taskList"
                        >
                            Completed
                        </button>
                    </div>
                    <button 
                        class="clear-completed-btn" 
                        id="clearCompletedBtn"
                        aria-label="Clear all completed tasks"
                    >
                        Clear Completed
                    </button>
                </div>

                <ul class="task-list" id="taskList" role="list" aria-live="polite">
                    <!-- Tasks will be dynamically inserted here -->
                </ul>

                <div class="empty-state" id="emptyState">
                    <div class="empty-icon">📝</div>
                    <h3 class="empty-title">No tasks yet</h3>
                    <p class="empty-message">Add a task above to get started!</p>
                </div>
            </section>
        </main>

        <footer class="app-footer">
            <p>&copy; 2025 Modern To-Do List. Built with vanilla JavaScript.</p>
        </footer>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer" aria-live="polite" aria-label="Notifications">
        <!-- Toast notifications will be dynamically inserted here -->
    </div>

    <script src="script.js"></script>
</body>
</html>
