/* CSS Custom Properties (Variables) */
:root {
    --primary-color: #6366f1;
    --primary-hover: #5855eb;
    --primary-light: #e0e7ff;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8fafc;
        --text-secondary: #cbd5e1;
        --text-muted: #64748b;
        
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --border-color: #334155;
        --border-light: #475569;
    }
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container and Layout */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.app-header {
    text-align: center;
    margin-bottom: 3rem;
}

.app-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.title-icon {
    background: var(--primary-color);
    color: white;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-lg);
}

.app-subtitle {
    color: var(--text-secondary);
    font-size: 1.125rem;
    font-weight: 400;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Task Input Section */
.task-input-section {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.input-group {
    display: flex;
    gap: 1rem;
    align-items: stretch;
}

.task-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-family: inherit;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

.task-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.task-input::placeholder {
    color: var(--text-muted);
}

.add-btn {
    padding: 1rem 2rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.add-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.add-btn:active {
    transform: translateY(0);
}

.btn-icon {
    font-size: 1.25rem;
    font-weight: 300;
}

/* Task Stats */
.task-stats {
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Task List Section */
.task-list-section {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.task-controls {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-buttons {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 0.25rem;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-color);
    color: white;
}

.clear-completed-btn {
    padding: 0.75rem 1.5rem;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.clear-completed-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.clear-completed-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Task List */
.task-list {
    list-style: none;
    max-height: 400px;
    overflow-y: auto;
}

.task-list:empty + .empty-state {
    display: block;
}

/* Task Item Styles */
.task-item {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-light);
    transition: all var(--transition-fast);
    background: var(--bg-primary);
}

.task-item:last-child {
    border-bottom: none;
}

.task-item:hover {
    background: var(--bg-secondary);
}

.task-item.completed {
    opacity: 0.7;
}

.task-item.completed .task-text {
    text-decoration: line-through;
    color: var(--text-muted);
}

.task-item.editing {
    background: var(--primary-light);
}

.task-checkbox {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 1rem;
    cursor: pointer;
    accent-color: var(--primary-color);
}

.task-content {
    flex: 1;
    display: flex;
    align-items: center;
    min-height: 2.5rem;
}

.task-text {
    flex: 1;
    font-size: 1rem;
    color: var(--text-primary);
    word-break: break-word;
    line-height: 1.5;
}

.task-edit-input {
    flex: 1;
    padding: 0.5rem;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-family: inherit;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.task-edit-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px var(--primary-light);
}

.task-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.task-item:hover .task-actions {
    opacity: 1;
}

.task-btn {
    padding: 0.5rem;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
}

.edit-btn {
    background: var(--warning-color);
    color: white;
}

.edit-btn:hover {
    background: #d97706;
    transform: scale(1.1);
}

.delete-btn {
    background: var(--danger-color);
    color: white;
}

.delete-btn:hover {
    background: #dc2626;
    transform: scale(1.1);
}

.save-btn {
    background: var(--success-color);
    color: white;
}

.save-btn:hover {
    background: #059669;
    transform: scale(1.1);
}

.cancel-btn {
    background: var(--secondary-color);
    color: white;
}

.cancel-btn:hover {
    background: #475569;
    transform: scale(1.1);
}

/* Animation Classes */
.task-item.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.task-item.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* Filter animations */
.task-item.hidden {
    display: none;
}

.task-item.show {
    display: flex;
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    pointer-events: none;
    max-width: 400px;
}

.notification {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1rem 1.25rem;
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    min-width: 300px;
    max-width: 400px;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
    transform: translateX(100%);
    opacity: 0;
    transition: all var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

/* Notification Types */
.notification.success {
    border-left: 4px solid var(--success-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, #f0fdf4 100%);
}

.notification.error {
    border-left: 4px solid var(--danger-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, #fef2f2 100%);
}

.notification.info {
    border-left: 4px solid var(--primary-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--primary-light) 100%);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
    background: linear-gradient(135deg, var(--bg-primary) 0%, #fffbeb 100%);
}

/* Notification Icon */
.notification-icon {
    font-size: 1.25rem;
    line-height: 1;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.error .notification-icon {
    color: var(--danger-color);
}

.notification.info .notification-icon {
    color: var(--primary-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

/* Notification Content */
.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.notification-message {
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.4;
    word-wrap: break-word;
}

/* Notification Close Button */
.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1rem;
    line-height: 1;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    flex-shrink: 0;
    margin-top: -0.25rem;
    margin-right: -0.25rem;
}

.notification-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Progress Bar */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--success-color);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    transition: width linear;
}

.notification.success .notification-progress {
    background: var(--success-color);
}

.notification.error .notification-progress {
    background: var(--danger-color);
}

.notification.info .notification-progress {
    background: var(--primary-color);
}

.notification.warning .notification-progress {
    background: var(--warning-color);
}

/* Dark mode adjustments for notifications */
@media (prefers-color-scheme: dark) {
    .notification.success {
        background: linear-gradient(135deg, var(--bg-primary) 0%, #064e3b 100%);
    }

    .notification.error {
        background: linear-gradient(135deg, var(--bg-primary) 0%, #7f1d1d 100%);
    }

    .notification.info {
        background: linear-gradient(135deg, var(--bg-primary) 0%, #1e3a8a 100%);
    }

    .notification.warning {
        background: linear-gradient(135deg, var(--bg-primary) 0%, #78350f 100%);
    }
}

/* Mobile Responsive Notifications */
@media (max-width: 768px) {
    .notification-container {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .notification {
        min-width: auto;
        max-width: none;
    }
}

/* Empty State */
.empty-state {
    display: none;
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-muted);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.empty-message {
    font-size: 0.875rem;
}

/* Footer */
.app-footer {
    margin-top: 2rem;
    text-align: center;
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    .title-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.25rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .task-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .btn-text {
        display: none;
    }
}

@media (max-width: 480px) {
    .task-input-section,
    .task-stats,
    .task-list-section {
        padding: 1rem;
    }
    
    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}
