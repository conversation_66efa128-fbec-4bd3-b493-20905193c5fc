/**
 * Modern To-Do List Application
 * Built with vanilla JavaScript ES6+
 */

/**
 * Notification Manager Class
 * Handles toast notifications for user feedback
 */
class NotificationManager {
    constructor() {
        this.container = document.getElementById('notificationContainer');
        this.notifications = new Map();
        this.notificationId = 0;
    }

    /**
     * Show a notification
     * @param {string} title - Notification title
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, info, warning)
     * @param {number} duration - Auto-dismiss duration in milliseconds (default: 4000)
     */
    show(title, message = '', type = 'info', duration = 4000) {
        const id = ++this.notificationId;
        const notification = this.createNotification(id, title, message, type, duration);

        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // Trigger animation
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });

        // Auto-dismiss
        if (duration > 0) {
            this.startProgressBar(notification, duration);
            setTimeout(() => this.hide(id), duration);
        }

        return id;
    }

    /**
     * Hide a notification
     * @param {number} id - Notification ID
     */
    hide(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        notification.classList.remove('show');
        notification.classList.add('hide');

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 300);
    }

    /**
     * Create notification element
     */
    createNotification(id, title, message, type, duration) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-live', 'polite');
        notification.dataset.notificationId = id;

        const icon = this.getIcon(type);

        notification.innerHTML = `
            <div class="notification-icon" aria-hidden="true">${icon}</div>
            <div class="notification-content">
                <div class="notification-title">${this.escapeHtml(title)}</div>
                ${message ? `<div class="notification-message">${this.escapeHtml(message)}</div>` : ''}
            </div>
            <button class="notification-close" aria-label="Close notification" onclick="notificationManager.hide(${id})">
                ✕
            </button>
            ${duration > 0 ? '<div class="notification-progress"></div>' : ''}
        `;

        return notification;
    }

    /**
     * Start progress bar animation
     */
    startProgressBar(notification, duration) {
        const progressBar = notification.querySelector('.notification-progress');
        if (!progressBar) return;

        progressBar.style.width = '100%';
        progressBar.style.transitionDuration = `${duration}ms`;

        requestAnimationFrame(() => {
            progressBar.style.width = '0%';
        });
    }

    /**
     * Get icon for notification type
     */
    getIcon(type) {
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };
        return icons[type] || icons.info;
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Convenience methods for different notification types
     */
    success(title, message, duration = 4000) {
        return this.show(title, message, 'success', duration);
    }

    error(title, message, duration = 6000) {
        return this.show(title, message, 'error', duration);
    }

    warning(title, message, duration = 5000) {
        return this.show(title, message, 'warning', duration);
    }

    info(title, message, duration = 4000) {
        return this.show(title, message, 'info', duration);
    }

    /**
     * Clear all notifications
     */
    clearAll() {
        this.notifications.forEach((_, id) => {
            this.hide(id);
        });
    }
}

class TodoApp {
    constructor() {
        this.tasks = this.loadTasks();
        this.currentFilter = 'all';
        this.editingTaskId = null;
        this.notifications = new NotificationManager();

        this.initializeElements();
        this.bindEvents();
        this.render();
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        this.taskForm = document.getElementById('taskForm');
        this.taskInput = document.getElementById('taskInput');
        this.taskList = document.getElementById('taskList');
        this.emptyState = document.getElementById('emptyState');
        this.clearCompletedBtn = document.getElementById('clearCompletedBtn');
        
        // Stats elements
        this.totalTasksEl = document.getElementById('totalTasks');
        this.activeTasksEl = document.getElementById('activeTasks');
        this.completedTasksEl = document.getElementById('completedTasks');
        
        // Filter buttons
        this.filterButtons = document.querySelectorAll('.filter-btn');
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Form submission
        this.taskForm.addEventListener('submit', (e) => this.handleAddTask(e));
        
        // Filter buttons
        this.filterButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleFilterChange(e));
        });
        
        // Clear completed button
        this.clearCompletedBtn.addEventListener('click', () => this.clearCompleted());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    /**
     * Handle form submission to add new task
     */
    handleAddTask(e) {
        e.preventDefault();

        const text = this.taskInput.value.trim();
        if (!text) {
            this.notifications.warning('Empty Task', 'Please enter a task description');
            return;
        }

        try {
            const task = {
                id: this.generateId(),
                text,
                completed: false,
                createdAt: new Date().toISOString()
            };

            this.tasks.unshift(task);
            this.taskInput.value = '';
            this.saveTasks();
            this.render();

            // Show success notification
            this.notifications.success('Task Created', `"${this.truncateText(text, 30)}" has been added to your list`);

            // Focus back to input for better UX
            this.taskInput.focus();
        } catch (error) {
            this.notifications.error('Failed to Create Task', 'An error occurred while creating the task');
            console.error('Error creating task:', error);
        }
    }

    /**
     * Handle filter button clicks
     */
    handleFilterChange(e) {
        const filter = e.target.dataset.filter;
        if (!filter) return;
        
        this.currentFilter = filter;
        
        // Update active filter button
        this.filterButtons.forEach(btn => {
            btn.classList.remove('active');
            btn.setAttribute('aria-selected', 'false');
        });
        
        e.target.classList.add('active');
        e.target.setAttribute('aria-selected', 'true');
        
        this.render();
    }

    /**
     * Toggle task completion status
     */
    toggleTask(id) {
        try {
            const task = this.tasks.find(t => t.id === id);
            if (task) {
                task.completed = !task.completed;
                this.saveTasks();
                this.render();

                // Show status change notification
                const status = task.completed ? 'completed' : 'reactivated';
                const icon = task.completed ? '✓' : '↻';
                this.notifications.success(
                    `Task ${status}`,
                    `${icon} "${this.truncateText(task.text, 30)}" has been ${status}`
                );
            }
        } catch (error) {
            this.notifications.error('Failed to Update Task', 'An error occurred while updating the task status');
            console.error('Error toggling task:', error);
        }
    }

    /**
     * Delete a task
     */
    deleteTask(id) {
        try {
            const task = this.tasks.find(t => t.id === id);
            if (!task) {
                this.notifications.error('Task Not Found', 'The task you tried to delete could not be found');
                return;
            }

            const taskElement = document.querySelector(`[data-task-id="${id}"]`);
            if (taskElement) {
                taskElement.classList.add('fade-out');
                setTimeout(() => {
                    this.tasks = this.tasks.filter(t => t.id !== id);
                    this.saveTasks();
                    this.render();

                    // Show deletion notification
                    this.notifications.success(
                        'Task Deleted',
                        `🗑 "${this.truncateText(task.text, 30)}" has been removed`
                    );
                }, 300);
            }
        } catch (error) {
            this.notifications.error('Failed to Delete Task', 'An error occurred while deleting the task');
            console.error('Error deleting task:', error);
        }
    }

    /**
     * Start editing a task
     */
    startEdit(id) {
        this.editingTaskId = id;
        this.render();
        
        // Focus the edit input
        const editInput = document.querySelector(`[data-task-id="${id}"] .task-edit-input`);
        if (editInput) {
            editInput.focus();
            editInput.select();
        }
    }

    /**
     * Save task edit
     */
    saveEdit(id, newText) {
        try {
            const text = newText.trim();
            if (!text) {
                this.notifications.warning('Empty Task', 'Task description cannot be empty');
                this.cancelEdit();
                return;
            }

            const task = this.tasks.find(t => t.id === id);
            if (task) {
                task.text = text;
                this.editingTaskId = null;
                this.saveTasks();
                this.render();

                // Show edit confirmation
                this.notifications.success(
                    'Task Updated',
                    `✎ Task has been updated successfully`
                );
            }
        } catch (error) {
            this.notifications.error('Failed to Update Task', 'An error occurred while saving the changes');
            console.error('Error saving task edit:', error);
        }
    }

    /**
     * Cancel task edit
     */
    cancelEdit() {
        this.editingTaskId = null;
        this.render();
        this.notifications.info('Edit Cancelled', 'Changes have been discarded');
    }

    /**
     * Clear all completed tasks
     */
    clearCompleted() {
        try {
            const completedTasks = this.tasks.filter(t => t.completed);
            if (completedTasks.length === 0) {
                this.notifications.info('No Completed Tasks', 'There are no completed tasks to clear');
                return;
            }

            if (confirm(`Are you sure you want to delete ${completedTasks.length} completed task(s)?`)) {
                this.tasks = this.tasks.filter(t => !t.completed);
                this.saveTasks();
                this.render();

                // Show clear confirmation
                this.notifications.success(
                    'Tasks Cleared',
                    `🧹 ${completedTasks.length} completed task${completedTasks.length > 1 ? 's' : ''} removed`
                );
            }
        } catch (error) {
            this.notifications.error('Failed to Clear Tasks', 'An error occurred while clearing completed tasks');
            console.error('Error clearing completed tasks:', error);
        }
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Escape key cancels editing
        if (e.key === 'Escape' && this.editingTaskId) {
            this.cancelEdit();
        }
        
        // Ctrl/Cmd + Enter adds new task
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            this.taskInput.focus();
        }
    }

    /**
     * Get filtered tasks based on current filter
     */
    getFilteredTasks() {
        switch (this.currentFilter) {
            case 'active':
                return this.tasks.filter(t => !t.completed);
            case 'completed':
                return this.tasks.filter(t => t.completed);
            default:
                return this.tasks;
        }
    }

    /**
     * Generate unique ID for tasks
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    /**
     * Truncate text for notifications
     */
    truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    /**
     * Load tasks from localStorage
     */
    loadTasks() {
        try {
            const stored = localStorage.getItem('todoTasks');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            this.notifications?.error('Loading Error', 'Failed to load saved tasks from storage');
            console.error('Error loading tasks:', error);
            return [];
        }
    }

    /**
     * Save tasks to localStorage
     */
    saveTasks() {
        try {
            localStorage.setItem('todoTasks', JSON.stringify(this.tasks));
        } catch (error) {
            this.notifications?.error('Saving Error', 'Failed to save tasks to storage');
            console.error('Error saving tasks:', error);
        }
    }

    /**
     * Update statistics display
     */
    updateStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(t => t.completed).length;
        const active = total - completed;
        
        this.totalTasksEl.textContent = total;
        this.activeTasksEl.textContent = active;
        this.completedTasksEl.textContent = completed;
        
        // Update clear completed button state
        this.clearCompletedBtn.disabled = completed === 0;
    }

    /**
     * Create task item HTML
     */
    createTaskHTML(task) {
        const isEditing = this.editingTaskId === task.id;
        const taskClass = `task-item ${task.completed ? 'completed' : ''} ${isEditing ? 'editing' : ''}`;
        
        return `
            <li class="${taskClass}" data-task-id="${task.id}" role="listitem">
                <input 
                    type="checkbox" 
                    class="task-checkbox" 
                    ${task.completed ? 'checked' : ''}
                    aria-label="Mark task as ${task.completed ? 'incomplete' : 'complete'}"
                    onchange="todoApp.toggleTask('${task.id}')"
                >
                <div class="task-content">
                    ${isEditing ? `
                        <input 
                            type="text" 
                            class="task-edit-input" 
                            value="${this.escapeHtml(task.text)}"
                            onkeydown="if(event.key==='Enter') todoApp.saveEdit('${task.id}', this.value); if(event.key==='Escape') todoApp.cancelEdit()"
                            onblur="todoApp.saveEdit('${task.id}', this.value)"
                            aria-label="Edit task"
                        >
                    ` : `
                        <span class="task-text">${this.escapeHtml(task.text)}</span>
                    `}
                </div>
                <div class="task-actions">
                    ${isEditing ? `
                        <button 
                            class="task-btn save-btn" 
                            onclick="todoApp.saveEdit('${task.id}', this.parentElement.parentElement.querySelector('.task-edit-input').value)"
                            aria-label="Save changes"
                        >
                            ✓
                        </button>
                        <button 
                            class="task-btn cancel-btn" 
                            onclick="todoApp.cancelEdit()"
                            aria-label="Cancel editing"
                        >
                            ✕
                        </button>
                    ` : `
                        <button 
                            class="task-btn edit-btn" 
                            onclick="todoApp.startEdit('${task.id}')"
                            aria-label="Edit task"
                        >
                            ✎
                        </button>
                        <button 
                            class="task-btn delete-btn" 
                            onclick="todoApp.deleteTask('${task.id}')"
                            aria-label="Delete task"
                        >
                            🗑
                        </button>
                    `}
                </div>
            </li>
        `;
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Render the application
     */
    render() {
        const filteredTasks = this.getFilteredTasks();
        
        // Update stats
        this.updateStats();
        
        // Render task list
        if (filteredTasks.length === 0) {
            this.taskList.innerHTML = '';
            this.emptyState.style.display = 'block';
        } else {
            this.emptyState.style.display = 'none';
            this.taskList.innerHTML = filteredTasks
                .map(task => this.createTaskHTML(task))
                .join('');
            
            // Add fade-in animation to new tasks
            const newTasks = this.taskList.querySelectorAll('.task-item:not(.fade-in)');
            newTasks.forEach(task => {
                task.classList.add('fade-in');
            });
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.todoApp = new TodoApp();
});
