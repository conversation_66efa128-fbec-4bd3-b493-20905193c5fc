/**
 * Modern To-Do List Application
 * Built with vanilla JavaScript ES6+
 */

class TodoApp {
    constructor() {
        this.tasks = this.loadTasks();
        this.currentFilter = 'all';
        this.editingTaskId = null;
        
        this.initializeElements();
        this.bindEvents();
        this.render();
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        this.taskForm = document.getElementById('taskForm');
        this.taskInput = document.getElementById('taskInput');
        this.taskList = document.getElementById('taskList');
        this.emptyState = document.getElementById('emptyState');
        this.clearCompletedBtn = document.getElementById('clearCompletedBtn');
        
        // Stats elements
        this.totalTasksEl = document.getElementById('totalTasks');
        this.activeTasksEl = document.getElementById('activeTasks');
        this.completedTasksEl = document.getElementById('completedTasks');
        
        // Filter buttons
        this.filterButtons = document.querySelectorAll('.filter-btn');
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Form submission
        this.taskForm.addEventListener('submit', (e) => this.handleAddTask(e));
        
        // Filter buttons
        this.filterButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleFilterChange(e));
        });
        
        // Clear completed button
        this.clearCompletedBtn.addEventListener('click', () => this.clearCompleted());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    /**
     * Handle form submission to add new task
     */
    handleAddTask(e) {
        e.preventDefault();
        
        const text = this.taskInput.value.trim();
        if (!text) return;
        
        const task = {
            id: this.generateId(),
            text,
            completed: false,
            createdAt: new Date().toISOString()
        };
        
        this.tasks.unshift(task);
        this.taskInput.value = '';
        this.saveTasks();
        this.render();
        
        // Focus back to input for better UX
        this.taskInput.focus();
    }

    /**
     * Handle filter button clicks
     */
    handleFilterChange(e) {
        const filter = e.target.dataset.filter;
        if (!filter) return;
        
        this.currentFilter = filter;
        
        // Update active filter button
        this.filterButtons.forEach(btn => {
            btn.classList.remove('active');
            btn.setAttribute('aria-selected', 'false');
        });
        
        e.target.classList.add('active');
        e.target.setAttribute('aria-selected', 'true');
        
        this.render();
    }

    /**
     * Toggle task completion status
     */
    toggleTask(id) {
        const task = this.tasks.find(t => t.id === id);
        if (task) {
            task.completed = !task.completed;
            this.saveTasks();
            this.render();
        }
    }

    /**
     * Delete a task
     */
    deleteTask(id) {
        const taskElement = document.querySelector(`[data-task-id="${id}"]`);
        if (taskElement) {
            taskElement.classList.add('fade-out');
            setTimeout(() => {
                this.tasks = this.tasks.filter(t => t.id !== id);
                this.saveTasks();
                this.render();
            }, 300);
        }
    }

    /**
     * Start editing a task
     */
    startEdit(id) {
        this.editingTaskId = id;
        this.render();
        
        // Focus the edit input
        const editInput = document.querySelector(`[data-task-id="${id}"] .task-edit-input`);
        if (editInput) {
            editInput.focus();
            editInput.select();
        }
    }

    /**
     * Save task edit
     */
    saveEdit(id, newText) {
        const text = newText.trim();
        if (!text) {
            this.cancelEdit();
            return;
        }
        
        const task = this.tasks.find(t => t.id === id);
        if (task) {
            task.text = text;
            this.editingTaskId = null;
            this.saveTasks();
            this.render();
        }
    }

    /**
     * Cancel task edit
     */
    cancelEdit() {
        this.editingTaskId = null;
        this.render();
    }

    /**
     * Clear all completed tasks
     */
    clearCompleted() {
        const completedTasks = this.tasks.filter(t => t.completed);
        if (completedTasks.length === 0) return;
        
        if (confirm(`Are you sure you want to delete ${completedTasks.length} completed task(s)?`)) {
            this.tasks = this.tasks.filter(t => !t.completed);
            this.saveTasks();
            this.render();
        }
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Escape key cancels editing
        if (e.key === 'Escape' && this.editingTaskId) {
            this.cancelEdit();
        }
        
        // Ctrl/Cmd + Enter adds new task
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            this.taskInput.focus();
        }
    }

    /**
     * Get filtered tasks based on current filter
     */
    getFilteredTasks() {
        switch (this.currentFilter) {
            case 'active':
                return this.tasks.filter(t => !t.completed);
            case 'completed':
                return this.tasks.filter(t => t.completed);
            default:
                return this.tasks;
        }
    }

    /**
     * Generate unique ID for tasks
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Load tasks from localStorage
     */
    loadTasks() {
        try {
            const stored = localStorage.getItem('todoTasks');
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Error loading tasks:', error);
            return [];
        }
    }

    /**
     * Save tasks to localStorage
     */
    saveTasks() {
        try {
            localStorage.setItem('todoTasks', JSON.stringify(this.tasks));
        } catch (error) {
            console.error('Error saving tasks:', error);
        }
    }

    /**
     * Update statistics display
     */
    updateStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(t => t.completed).length;
        const active = total - completed;
        
        this.totalTasksEl.textContent = total;
        this.activeTasksEl.textContent = active;
        this.completedTasksEl.textContent = completed;
        
        // Update clear completed button state
        this.clearCompletedBtn.disabled = completed === 0;
    }

    /**
     * Create task item HTML
     */
    createTaskHTML(task) {
        const isEditing = this.editingTaskId === task.id;
        const taskClass = `task-item ${task.completed ? 'completed' : ''} ${isEditing ? 'editing' : ''}`;
        
        return `
            <li class="${taskClass}" data-task-id="${task.id}" role="listitem">
                <input 
                    type="checkbox" 
                    class="task-checkbox" 
                    ${task.completed ? 'checked' : ''}
                    aria-label="Mark task as ${task.completed ? 'incomplete' : 'complete'}"
                    onchange="todoApp.toggleTask('${task.id}')"
                >
                <div class="task-content">
                    ${isEditing ? `
                        <input 
                            type="text" 
                            class="task-edit-input" 
                            value="${this.escapeHtml(task.text)}"
                            onkeydown="if(event.key==='Enter') todoApp.saveEdit('${task.id}', this.value); if(event.key==='Escape') todoApp.cancelEdit()"
                            onblur="todoApp.saveEdit('${task.id}', this.value)"
                            aria-label="Edit task"
                        >
                    ` : `
                        <span class="task-text">${this.escapeHtml(task.text)}</span>
                    `}
                </div>
                <div class="task-actions">
                    ${isEditing ? `
                        <button 
                            class="task-btn save-btn" 
                            onclick="todoApp.saveEdit('${task.id}', this.parentElement.parentElement.querySelector('.task-edit-input').value)"
                            aria-label="Save changes"
                        >
                            ✓
                        </button>
                        <button 
                            class="task-btn cancel-btn" 
                            onclick="todoApp.cancelEdit()"
                            aria-label="Cancel editing"
                        >
                            ✕
                        </button>
                    ` : `
                        <button 
                            class="task-btn edit-btn" 
                            onclick="todoApp.startEdit('${task.id}')"
                            aria-label="Edit task"
                        >
                            ✎
                        </button>
                        <button 
                            class="task-btn delete-btn" 
                            onclick="todoApp.deleteTask('${task.id}')"
                            aria-label="Delete task"
                        >
                            🗑
                        </button>
                    `}
                </div>
            </li>
        `;
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Render the application
     */
    render() {
        const filteredTasks = this.getFilteredTasks();
        
        // Update stats
        this.updateStats();
        
        // Render task list
        if (filteredTasks.length === 0) {
            this.taskList.innerHTML = '';
            this.emptyState.style.display = 'block';
        } else {
            this.emptyState.style.display = 'none';
            this.taskList.innerHTML = filteredTasks
                .map(task => this.createTaskHTML(task))
                .join('');
            
            // Add fade-in animation to new tasks
            const newTasks = this.taskList.querySelectorAll('.task-item:not(.fade-in)');
            newTasks.forEach(task => {
                task.classList.add('fade-in');
            });
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.todoApp = new TodoApp();
});
